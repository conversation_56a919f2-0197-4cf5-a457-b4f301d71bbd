import { ipcMain } from "electron";
import { getEmployerDb } from "../main/EmployerDbManager";
import * as schema from "../../drizzle/schema/employer/payPeriod";
import { eq, inArray, sql } from "drizzle-orm";

// Fetch all pay periods
ipcMain.handle("employerDb:getPayPeriods", async (event, dbPath: string) => {
  try {
    const entry = getEmployerDb(dbPath);
    if (!entry) {
      console.error(
        "[IPC] getPayPeriods: Employer DB is not open for dbPath:",
        dbPath,
      );
      return { success: false, error: "Employer DB is not open" };
    }
    const db = entry.db;
    const payPeriods = await db.select().from(schema.payPeriods).all();
    return { success: true, payPeriods };
  } catch (err: any) {
    console.error("[IPC] getPayPeriods error:", err);
    return { success: false, error: err.message };
  }
});

// Rename a pay period schedule (update label)
ipcMain.handle(
  "employerDb:renamePayPeriodSchedule",
  async (event, { dbPath, scheduleId, label }) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      if (
        !label ||
        typeof label !== "string" ||
        label.length === 0 ||
        label.length > 20
      ) {
        return {
          success: false,
          error: "Label is required and must be at most 20 characters.",
        };
      }
      if (!scheduleId || typeof scheduleId !== "string") {
        return { success: false, error: "ScheduleId is required." };
      }
      const now = Math.floor(Date.now() / 1000);
      const {
        payPeriodSchedules,
      } = require("../../drizzle/schema/employer/payPeriodSchedule");
      const result = await db
        .update(payPeriodSchedules)
        .set({ label, updated_at: now })
        .where(eq(payPeriodSchedules.id, scheduleId));
      return { success: true };
    } catch (err) {
      console.error("[IPC] renamePayPeriodSchedule error:", err);
      return {
        success: false,
        error:
          err && typeof err === "object" && "message" in err
            ? (err as any).message
            : String(err),
      };
    }
  },
);

// Add a pay period with new schedule (always create new schedule, label max 20 chars)
ipcMain.handle(
  "employerDb:addPayPeriod",
  async (event, dbPath: string, data: any) => {
    console.log("[DEBUG] employerDb:addPayPeriod payload:", data);
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      // Validate label
      if (
        !data.label ||
        typeof data.label !== "string" ||
        data.label.length === 0 ||
        data.label.length > 20
      ) {
        return {
          success: false,
          error: "Label is required and must be at most 20 characters.",
        };
      }
      // Validate type
      if (!data.type || typeof data.type !== "string") {
        return { success: false, error: "Type is required." };
      }
      // Create new schedule
      const scheduleId = crypto.randomUUID();
      const now = Math.floor(Date.now() / 1000);
      const [schedule] = await db
        .insert(
          require("../../drizzle/schema/employer/payPeriodSchedule")
            .payPeriodSchedules,
        )
        .values({
          id: scheduleId,
          label: data.label,
          type: data.type,
          created_at: now,
          updated_at: now,
        })
        .returning();
      // Insert pay period with new schedule_id
      const payPeriodId = crypto.randomUUID();
      const [payPeriod] = await db
        .insert(schema.payPeriods)
        .values({
          ...data,
          id: payPeriodId,
          schedule_id: scheduleId,
          created_at: now,
          updated_at: now,
        })
        .returning();
      return { success: true, schedule, payPeriod };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Fetch pay period schedules for a specific tax year
ipcMain.handle(
  "employerDb:getPayPeriodSchedules",
  async (event, dbPath: string, taxYear: string) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const {
        payPeriodSchedules,
      } = require("../../drizzle/schema/employer/payPeriodSchedule");
      const eq = require("drizzle-orm").eq;
      const schedules = await db
        .select()
        .from(payPeriodSchedules)
        .where(eq(payPeriodSchedules.tax_year, taxYear))
        .all();
      return { success: true, schedules };
    } catch (err: any) {
      console.error("[IPC] getPayPeriodSchedules error:", err);
      return { success: false, error: err.message };
    }
  },
);

// Delete a pay period schedule by id
ipcMain.handle(
  "employerDb:deletePayPeriodSchedule",
  async (event, { dbPath, scheduleId }) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const {
        payPeriodSchedules,
      } = require("../../drizzle/schema/employer/payPeriodSchedule");
      const deleted = await db
        .delete(payPeriodSchedules)
        .where(eq(payPeriodSchedules.id, scheduleId));
      return { success: true, deletedCount: deleted.changes ?? 0 };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Add schedule and all pay periods for it (batch)
ipcMain.handle(
  "employerDb:addPayPeriodScheduleAndPeriods",
  async (
    event,
    dbPath: string,
    payload: { label: string; type: string; tax_year: string; periods: any[] },
  ) => {
    console.log(
      "[DEBUG] employerDb:addPayPeriodScheduleAndPeriods payload:",
      payload,
    );
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      // Validate label
      if (
        !payload.label ||
        typeof payload.label !== "string" ||
        payload.label.length === 0 ||
        payload.label.length > 20
      ) {
        return {
          success: false,
          error: "Label is required and must be at most 20 characters.",
        };
      }
      // Validate type
      if (!payload.type || typeof payload.type !== "string") {
        return { success: false, error: "Type is required." };
      }
      const scheduleId = crypto.randomUUID();
      const now = Math.floor(Date.now() / 1000);
      // Batch create schedule, periods, and initial payslips
      const { schedule, payPeriods: insertedPeriods } = await db.transaction(
        async (tx: any) => {
          const {
            payPeriodSchedules,
          } = require("../../drizzle/schema/employer/payPeriodSchedule");
          const [schedule] = await tx
            .insert(payPeriodSchedules)
            .values({
              id: scheduleId,
              label: payload.label,
              type: payload.type,
              tax_year: payload.tax_year,
              created_at: now,
              updated_at: now,
            })
            .returning();
          // insert periods
          const periodsToInsert = payload.periods.map((period) => ({
            id: crypto.randomUUID(),
            schedule_id: scheduleId,
            ...period,
            created_at: now,
            updated_at: now,
          }));
          const insertedPeriods = await tx
            .insert(schema.payPeriods)
            .values(periodsToInsert)
            .returning();
          // initial payslips per employee per period
          const {
            employee,
          } = require("../../drizzle/schema/employer/employee.schema");
          // Fetch employees matching schedule type
          const emps = await tx
            .select({
              id: employee.id,
              startDate: employee.startDate,
              leaveDate: employee.leaveDate,
              freq: employee.paymentFrequency,
            })
            .from(employee)
            // Case-insensitive match between paymentFrequency and schedule type
            .where(sql`lower(${employee.paymentFrequency}) = ${payload.type}`)
            .all();
          // Link employees to this schedule
          const empIds = emps.map((e: { id: string }) => e.id);
          if (empIds.length > 0) {
            await tx
              .update(employee)
              .set({ periodScheduleId: scheduleId })
              .where(inArray(employee.id, empIds));
          }
          const earliestNum = Math.min(
            ...insertedPeriods.map((p: any) => p.period_number),
          );
          const earliestPeriod = insertedPeriods.find(
            (p: any) => p.period_number === earliestNum,
          );
          const payslipValues: any[] = [];
          const { payslips } = require("../../drizzle/schema/employer/payslip");
          for (const emp of emps) {
            const startDate = new Date(emp.startDate);
            const leaveDate = emp.leaveDate ? new Date(emp.leaveDate) : null;
            for (const period of insertedPeriods) {
              const periodEnd = new Date(period.period_end);
              if (periodEnd < startDate) continue;
              if (leaveDate && periodEnd >= leaveDate) continue;
              payslipValues.push({
                id: crypto.randomUUID(),
                employee_id: emp.id,
                period_id: period.id,
                status: period.id === earliestPeriod.id ? "open" : "future",
                created_at: now,
                updated_at: now,
              });
            }
          }
          if (payslipValues.length) {
            await tx.insert(payslips).values(payslipValues);
          }
          return { schedule, payPeriods: insertedPeriods };
        },
      );
      return { success: true, schedule, payPeriods: insertedPeriods };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Add multiple pay periods (batch insert)
ipcMain.handle(
  "employerDb:addPayPeriods",
  async (event, dbPath: string, payPeriods: any[]) => {
    console.log("[DEBUG] employerDb:addPayPeriods payload:", payPeriods);
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const inserted = await db
        .insert(schema.payPeriods)
        .values(payPeriods)
        .returning();
      return { success: true, payPeriods: inserted };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Update a pay period
ipcMain.handle(
  "employerDb:updatePayPeriod",
  async (event, dbPath: string, payPeriod: any) => {
    console.log("[DEBUG] employerDb:updatePayPeriod payload:", payPeriod);
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const [updated] = await db
        .update(schema.payPeriods)
        .set(payPeriod)
        .where(eq(schema.payPeriods.id, payPeriod.id))
        .returning();
      return { success: true, payPeriod: updated };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Delete a pay period
ipcMain.handle(
  "employerDb:deletePayPeriod",
  async (event, dbPath: string, id: string) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;

      // Validate inputs
      if (!id || typeof id !== "string") {
        return { success: false, error: "Invalid pay period ID" };
      }

      // Use transaction to ensure proper deletion order
      const result = await db.transaction(async (tx: any) => {
        // Delete associated payslip data first
        const {
          payslips,
          payslipLineItems,
          payslipNotes,
          payslipAdditionsLineItems,
          payslipDeductionsLineItems,
        } = require("../../drizzle/schema/employer/payslip");

        // Find payslips to delete
        const payslipsToDelete = await tx
          .select({ id: payslips.id })
          .from(payslips)
          .where(eq(payslips.period_id, id))
          .all();
        const payslipIds = payslipsToDelete.map((p: { id: string }) => p.id);

        if (payslipIds.length > 0) {
          // Delete dependent line items and notes in correct order
          await tx
            .delete(payslipLineItems)
            .where(inArray(payslipLineItems.payslip_id, payslipIds));
          await tx
            .delete(payslipAdditionsLineItems)
            .where(inArray(payslipAdditionsLineItems.payslip_id, payslipIds));
          await tx
            .delete(payslipDeductionsLineItems)
            .where(inArray(payslipDeductionsLineItems.payslip_id, payslipIds));
          await tx
            .delete(payslipNotes)
            .where(inArray(payslipNotes.payslip_id, payslipIds));
        }

        // Delete payslips
        await tx.delete(payslips).where(eq(payslips.period_id, id));

        // Finally delete the pay period
        await tx.delete(schema.payPeriods).where(eq(schema.payPeriods.id, id));

        return { success: true, id, deletedPayslips: payslipIds.length };
      });

      return result;
    } catch (err: any) {
      console.error("[IPC] Error deleting pay period:", err);
      return { success: false, error: err.message || "Unknown error occurred" };
    }
  },
);

import { PAY_PERIOD_TYPES } from "../../drizzle/schema/employer/payPeriod";

// Type guard for pay period type
function isPayPeriodType(
  type: string,
): type is (typeof PAY_PERIOD_TYPES)[number] {
  return (PAY_PERIOD_TYPES as readonly string[]).includes(type);
}

// Delete all pay periods by type and scheduleId
ipcMain.handle(
  "employerDb:deletePayPeriodsByTypeAndName",
  async (event, dbPath: string, type: string, scheduleId: string | null) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      // Import the payPeriodSchedules table from the correct schema file
      const {
        payPeriodSchedules,
      } = require("../../drizzle/schema/employer/payPeriodSchedule");
      // Validate and narrow type
      if (!isPayPeriodType(type)) {
        return { success: false, error: `Invalid period type: ${type}` };
      }
      const periodType = type;
      let whereClause;
      if (scheduleId !== null) {
        // Delete all periods for this schedule regardless of type
        whereClause = eq(schema.payPeriods.schedule_id, scheduleId);
      } else {
        whereClause = eq(schema.payPeriods.type, periodType);
      }
      // Debug: show reset filter parameters
      console.log("[DEBUG] deletePayPeriodsByTypeAndName filter:", {
        periodType,
        scheduleId,
      });
      // Validate inputs
      if (!isPayPeriodType(type)) {
        return { success: false, error: `Invalid period type: ${type}` };
      }

      // Begin transaction
      const result = await db.transaction(async (tx: any) => {
        // Delete associated payslip notes, line items, and payslips before pay periods
        const {
          payslips,
          payslipLineItems,
          payslipNotes,
          payslipAdditionsLineItems,
          payslipDeductionsLineItems,
        } = require("../../drizzle/schema/employer/payslip");
        const periodsToDelete = await tx
          .select({ id: schema.payPeriods.id })
          .from(schema.payPeriods)
          .where(whereClause)
          .all();
        const periodIds = periodsToDelete.map((p: { id: string }) => p.id);
        if (periodIds.length > 0) {
          // Find payslips to delete
          const payslipsToDelete = await tx
            .select({ id: payslips.id })
            .from(payslips)
            .where(inArray(payslips.period_id, periodIds))
            .all();
          const payslipIds = payslipsToDelete.map((p: { id: string }) => p.id);
          if (payslipIds.length > 0) {
            // Delete dependent line items and notes in correct order
            await tx
              .delete(payslipLineItems)
              .where(inArray(payslipLineItems.payslip_id, payslipIds));
            await tx
              .delete(payslipAdditionsLineItems)
              .where(inArray(payslipAdditionsLineItems.payslip_id, payslipIds));
            await tx
              .delete(payslipDeductionsLineItems)
              .where(
                inArray(payslipDeductionsLineItems.payslip_id, payslipIds),
              );
            await tx
              .delete(payslipNotes)
              .where(inArray(payslipNotes.payslip_id, payslipIds));
          }
          // Delete payslips
          await tx
            .delete(payslips)
            .where(inArray(payslips.period_id, periodIds));
        }
        // Delete pay periods
        await tx.delete(schema.payPeriods).where(whereClause);
        // If scheduleId is provided, delete the schedule as well
        if (scheduleId !== null) {
          // Clear employee schedule links before deleting schedule to avoid FK errors
          const {
            employee,
          } = require("../../drizzle/schema/employer/employee.schema");
          await tx
            .update(employee)
            .set({ periodScheduleId: null })
            .where(eq(employee.periodScheduleId, scheduleId));
          console.log(
            `[DEBUG] Attempting to delete schedule with id: ${scheduleId}`,
          );
          const scheduleDeleteResult = await tx
            .delete(payPeriodSchedules)
            .where(eq(payPeriodSchedules.id, scheduleId));
          console.log(`[DEBUG] Schedule delete result:`, scheduleDeleteResult);
        }

        return { success: true, deletedPeriods: periodIds.length };
      });

      return result;
    } catch (err: any) {
      console.error("[IPC] Error deleting pay periods:", err);
      return { success: false, error: err.message || "Unknown error occurred" };
    }
  },
);
