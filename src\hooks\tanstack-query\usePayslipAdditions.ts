import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useEmployerDBContext } from "@/providers/employer-db-provider";
import {
  getAdditionsItemTypes,
  upsertPayslipAdditionsLineItem,
  deletePayslipAdditionsLineItem,
  clearAllPayslipAdditionsLineItems,
  deleteAllPayslipAdditionsLineItems,
} from "@/services/employerDbService";
import type {
  PayslipAdditionsItemType,
  PayslipAdditionsLineItem,
} from "@/drizzle/schema/employer/payslip";

// Fetch additions item types
export function usePayslipAdditionsItemTypes() {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  return useQuery<PayslipAdditionsItemType[]>({
    queryKey: ["payslip-additions-item-types", dbPath],
    queryFn: () => getAdditionsItemTypes(dbPath!),
    enabled: !!dbPath,
  });
}

// Upsert additions line item
export function useUpsertPayslipAdditionsLineItemMutation(
  employeeId: string,
  periodId: string,
) {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (
      item: Partial<PayslipAdditionsLineItem> & { payslip_id: string },
    ) => upsertPayslipAdditionsLineItem(dbPath!, item),
    onSuccess: () => {
      // Reduced cache invalidation frequency to prevent UI flashing
      if (dbPath) {
        setTimeout(() => {
          queryClient.invalidateQueries({
            queryKey: ["payslip-additions", dbPath, employeeId, periodId],
          });
        }, 100);
      }
    },
  });
}

// Delete additions line item
export function useDeletePayslipAdditionsLineItemMutation(
  employeeId: string,
  periodId: string,
) {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: string) => deletePayslipAdditionsLineItem(dbPath!, id),
    onSuccess: () => {
      if (dbPath) {
        queryClient.invalidateQueries({
          queryKey: ["payslip", dbPath, employeeId, periodId],
        });
        queryClient.invalidateQueries({
          queryKey: ["payslip-additions", dbPath, employeeId, periodId],
        });
      }
    },
  });
}

// Clear all additions line items (set amounts to 0)
export function useClearAllPayslipAdditionsLineItemsMutation(
  employeeId: string,
  periodId: string,
) {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (payslipId: string) =>
      clearAllPayslipAdditionsLineItems(dbPath!, payslipId),
    onSuccess: () => {
      if (dbPath) {
        queryClient.invalidateQueries({
          queryKey: ["payslip", dbPath, employeeId, periodId],
        });
        queryClient.invalidateQueries({
          queryKey: ["payslip-additions", dbPath, employeeId, periodId],
        });
      }
    },
  });
}

// Delete all additions line items
export function useDeleteAllPayslipAdditionsLineItemsMutation(
  employeeId: string,
  periodId: string,
) {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (payslipId: string) =>
      deleteAllPayslipAdditionsLineItems(dbPath!, payslipId),
    onSuccess: () => {
      if (dbPath) {
        queryClient.invalidateQueries({
          queryKey: ["payslip", dbPath, employeeId, periodId],
        });
        queryClient.invalidateQueries({
          queryKey: ["payslip-additions", dbPath, employeeId, periodId],
        });
      }
    },
  });
}
