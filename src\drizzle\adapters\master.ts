import { createClient } from "@libsql/client";
import { drizzle } from "drizzle-orm/libsql";
import path from "path";
import { app } from "electron";

let db: ReturnType<typeof drizzle> | null = null;
let client: ReturnType<typeof createClient> | null = null;

export function getMasterDb() {
  if (db) return db;

  try {
    const dbPath = path.join(app.getPath("userData"), "master.db");
    client = createClient({
      url: `file:${dbPath}`,
    });
    db = drizzle(client);
    console.log("[MasterDB] Database connection established:", dbPath);
    return db;
  } catch (error) {
    console.error("[MasterDB] Failed to create database connection:", error);
    throw error;
  }
}

export function getMasterDbClient() {
  return client;
}
