import { getEmployerDbAdapter } from "../../drizzle/adapters/employerDbAdapter";

// Map of filePath -> { db, client }
const employerDbMap = new Map<string, { db: any; client: any }>();

/**
 * Opens (or returns existing) persistent employer DB connection.
 * @param filePath Absolute path to employer DB file
 */
export async function openEmployerDb(
  filePath: string,
  validate?: (db: any) => Promise<void>,
) {
  if (employerDbMap.has(filePath)) {
    return employerDbMap.get(filePath)!;
  }
  let db: any, client: any;
  try {
    ({ db, client } = getEmployerDbAdapter(filePath));
    try {
      // DEBUG: List all tables and their columns
      const tables = await db.all(
        "SELECT name FROM sqlite_master WHERE type='table'",
      );
      for (const t of tables) {
        try {
          const pragma = await db.all(`PRAGMA table_info(${t.name})`);
        } catch (e) {
          console.log(`[DEBUG] Error getting columns for table ${t.name}:`, e);
        }
      }
    } catch (e) {
      console.log("[DEBUG] Error listing tables/columns:", e);
    }
    if (validate) {
      try {
        await validate(db);
      } catch (validationError) {
        try {
          client?.close();
        } catch {}
        throw validationError;
      }
    }
    employerDbMap.set(filePath, { db, client });
    return { db, client };
  } catch (err) {
    // Defensive: try to close client if error occurs
    try {
      client?.close();
    } catch {}
    if (employerDbMap.has(filePath)) {
      employerDbMap.delete(filePath);
    }
    throw err;
  }
}

/**
 * Returns the persistent employer DB connection if open, else undefined.
 */
export function getEmployerDb(filePath: string) {
  return employerDbMap.get(filePath);
}

/**
 * Closes and removes a persistent employer DB connection.
 */
export function closeEmployerDb(filePath: string) {
  const entry = employerDbMap.get(filePath);
  if (entry) {
    try {
      entry.client.close();
    } catch {}
    employerDbMap.delete(filePath);
  }
}

/**
 * Closes all open employer DB connections (for app shutdown).
 */
export function closeAllEmployerDbs() {
  console.log(`[EmployerDbManager] Closing ${employerDbMap.size} open database connections`);
  for (const [filePath, entry] of employerDbMap.entries()) {
    try {
      if (entry.client && typeof entry.client.close === 'function') {
        entry.client.close();
        console.log(`[EmployerDbManager] Closed database: ${filePath}`);
      }
    } catch (error) {
      console.warn(`[EmployerDbManager] Error closing database ${filePath}:`, error);
    }
  }
  employerDbMap.clear();
  console.log(`[EmployerDbManager] All database connections closed`);
}

/**
 * Checks if a DB is open (for UI sync).
 */
export function isEmployerDbOpen(filePath: string) {
  return employerDbMap.has(filePath);
}
