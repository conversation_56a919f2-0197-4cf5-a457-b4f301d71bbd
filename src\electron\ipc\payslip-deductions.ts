import { ipcMain } from "electron";
import { getEmployerDb } from "../main/EmployerDbManager";
import * as schema from "../../drizzle/schema/employer/payslip";
import { eq } from "drizzle-orm";

// Rate limiting for database operations
const operationQueue = new Map<string, Promise<any>>();

async function queueOperation<T>(
  key: string,
  operation: () => Promise<T>,
): Promise<T> {
  // Wait for any existing operation with the same key
  if (operationQueue.has(key)) {
    try {
      await operationQueue.get(key);
    } catch {
      // Ignore errors from previous operations
    }
  }

  // Execute the new operation
  const promise = operation();
  operationQueue.set(key, promise);

  try {
    const result = await promise;
    operationQueue.delete(key);
    return result;
  } catch (error) {
    operationQueue.delete(key);
    throw error;
  }
}

// Fetch all deductions item types
ipcMain.handle(
  "employerDb:getDeductionsItemTypes",
  async (_event, dbPath: string) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      let itemTypes = await db
        .select()
        .from(schema.payslipDeductionsItemTypes)
        .all();

      // Seed default types if none exist
      if (itemTypes.length === 0) {
        const now = Math.floor(Date.now() / 1000);
        const defaults = [
          {
            id: "salary-sacrifice",
            code: "salary-sacrifice",
            display_label: "Salary Sacrifice",
            created_at: now,
            updated_at: now,
          },
          {
            id: "unpaid-leave",
            code: "unpaid-leave",
            display_label: "Unpaid Leave",
            created_at: now,
            updated_at: now,
          },
          {
            id: "advance",
            code: "advance",
            display_label: "Advances",
            created_at: now,
            updated_at: now,
          },
          {
            id: "payroll-giving",
            code: "payroll-giving",
            display_label: "Payroll Giving",
            created_at: now,
            updated_at: now,
          },
        ];
        await db
          .insert(schema.payslipDeductionsItemTypes)
          .values(defaults)
          .run();
        itemTypes = await db
          .select()
          .from(schema.payslipDeductionsItemTypes)
          .all();
      }
      return { success: true, itemTypes };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Upsert a deductions line item
ipcMain.handle(
  "employerDb:upsertPayslipDeductionsLineItem",
  async (_event, dbPath: string, item: any) => {
    const operationKey = `upsert-deduction-${dbPath}-${item?.payslip_id}`;

    return queueOperation(operationKey, async () => {
      try {
        const entry = getEmployerDb(dbPath);
        if (!entry) return { success: false, error: "Employer DB is not open" };
        const db = entry.db;

        // Validate inputs
        if (!item || typeof item !== "object") {
          return { success: false, error: "Invalid item data" };
        }
        if (!item.payslip_id || typeof item.payslip_id !== "string") {
          return { success: false, error: "Invalid payslip ID" };
        }

        const now = Math.floor(Date.now() / 1000);

        if (!item.id) {
          const id = crypto.randomUUID();
          const [inserted] = await db
            .insert(schema.payslipDeductionsLineItems)
            .values({ ...item, id, created_at: now, updated_at: now })
            .returning();
          return { success: true, lineItem: inserted };
        } else {
          const [updated] = await db
            .update(schema.payslipDeductionsLineItems)
            .set({ ...item, updated_at: now })
            .where(eq(schema.payslipDeductionsLineItems.id, item.id))
            .returning();
          return { success: true, lineItem: updated };
        }
      } catch (err: any) {
        console.error("[IPC] Error upserting deductions line item:", err);
        return {
          success: false,
          error: err.message || "Unknown error occurred",
        };
      }
    });
  },
);

// Delete a deductions line item
ipcMain.handle(
  "employerDb:deletePayslipDeductionsLineItem",
  async (_event, dbPath: string, id: string) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const result = await db
        .delete(schema.payslipDeductionsLineItems)
        .where(eq(schema.payslipDeductionsLineItems.id, id));
      return { success: true, deletedCount: result.changes ?? 0 };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Clear all deductions line items (set amounts to 0)
ipcMain.handle(
  "employerDb:clearAllPayslipDeductionsLineItems",
  async (_event, dbPath: string, payslipId: string) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const now = Math.floor(Date.now() / 1000);
      const result = await db
        .update(schema.payslipDeductionsLineItems)
        .set({ amount: 0, updated_at: now })
        .where(eq(schema.payslipDeductionsLineItems.payslip_id, payslipId));
      return { success: true, updatedCount: result.changes ?? 0 };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Delete all deductions line items
ipcMain.handle(
  "employerDb:deleteAllPayslipDeductionsLineItems",
  async (_event, dbPath: string, payslipId: string) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const result = await db
        .delete(schema.payslipDeductionsLineItems)
        .where(eq(schema.payslipDeductionsLineItems.payslip_id, payslipId));
      return { success: true, deletedCount: result.changes ?? 0 };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);
