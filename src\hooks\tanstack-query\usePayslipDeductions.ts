import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useEmployerDBContext } from "@/providers/employer-db-provider";
import {
  getDeductionsItemTypes,
  upsertPayslipDeductionsLineItem,
  deletePayslipDeductionsLineItem,
  clearAllPayslipDeductionsLineItems,
  deleteAllPayslipDeductionsLineItems,
} from "@/services/employerDbService";
import type {
  PayslipDeductionsItemType,
  PayslipDeductionsLineItem,
} from "@/drizzle/schema/employer/payslip";

// Fetch deductions item types
export function usePayslipDeductionsItemTypes() {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  return useQuery<PayslipDeductionsItemType[]>({
    queryKey: ["payslip-deductions-item-types", dbPath],
    queryFn: () => getDeductionsItemTypes(dbPath!),
    enabled: !!dbPath,
  });
}

// Upsert deductions line item
export function useUpsertPayslipDeductionsLineItemMutation(
  employeeId: string,
  periodId: string,
) {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (
      item: Partial<PayslipDeductionsLineItem> & { payslip_id: string },
    ) => upsertPayslipDeductionsLineItem(dbPath!, item),
    onSuccess: () => {
      if (dbPath) {
        // Sequential cache invalidation to prevent race conditions
        setTimeout(() => {
          queryClient.invalidateQueries({
            queryKey: ["payslip", dbPath, employeeId, periodId],
          });
        }, 0);
        setTimeout(() => {
          queryClient.invalidateQueries({
            queryKey: ["payslip-deductions", dbPath, employeeId, periodId],
          });
        }, 50);
      }
    },
  });
}

// Delete deductions line item
export function useDeletePayslipDeductionsLineItemMutation(
  employeeId: string,
  periodId: string,
) {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: string) => deletePayslipDeductionsLineItem(dbPath!, id),
    onSuccess: () => {
      if (dbPath) {
        queryClient.invalidateQueries({
          queryKey: ["payslip", dbPath, employeeId, periodId],
        });
        queryClient.invalidateQueries({
          queryKey: ["payslip-deductions", dbPath, employeeId, periodId],
        });
      }
    },
  });
}

// Clear all deductions line items (set amounts to 0)
export function useClearAllPayslipDeductionsLineItemsMutation(
  employeeId: string,
  periodId: string,
) {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (payslipId: string) =>
      clearAllPayslipDeductionsLineItems(dbPath!, payslipId),
    onSuccess: () => {
      if (dbPath) {
        queryClient.invalidateQueries({
          queryKey: ["payslip", dbPath, employeeId, periodId],
        });
        queryClient.invalidateQueries({
          queryKey: ["payslip-deductions", dbPath, employeeId, periodId],
        });
      }
    },
  });
}

// Delete all deductions line items
export function useDeleteAllPayslipDeductionsLineItemsMutation(
  employeeId: string,
  periodId: string,
) {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (payslipId: string) =>
      deleteAllPayslipDeductionsLineItems(dbPath!, payslipId),
    onSuccess: () => {
      if (dbPath) {
        queryClient.invalidateQueries({
          queryKey: ["payslip", dbPath, employeeId, periodId],
        });
        queryClient.invalidateQueries({
          queryKey: ["payslip-deductions", dbPath, employeeId, periodId],
        });
      }
    },
  });
}
