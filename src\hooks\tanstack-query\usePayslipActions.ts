import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useEmployerDBContext } from "@/providers/employer-db-provider";
import { finalisePayslips, reopenPayslips } from "@/services/employerDbService";

/**
 * Hook to finalise selected payslips and open next period
 */
export function useFinalisePayslipsMutation(periodId: string) {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (slipIds: string[]) => {
      if (!dbPath) throw new Error("Database not available");
      if (!periodId) throw new Error("Period ID not available");
      if (!Array.isArray(slipIds) || slipIds.length === 0) {
        throw new Error("No employees selected");
      }

      try {
        return await finalisePayslips(dbPath, periodId, slipIds);
      } catch (error) {
        console.error("[useFinalisePayslipsMutation] Error:", error);
        throw error;
      }
    },
    onSuccess: () => {
      if (dbPath) {
        // Sequential cache invalidation to prevent race conditions
        setTimeout(() => {
          queryClient.invalidateQueries({ queryKey: ["payPeriods", dbPath] });
        }, 0);

        setTimeout(() => {
          queryClient.invalidateQueries({
            queryKey: ["payslips", dbPath, periodId],
          });
        }, 50);

        setTimeout(() => {
          queryClient.invalidateQueries({
            queryKey: ["payslipStatuses", dbPath, periodId],
          });
        }, 100);
      }
    },
    onError: (error) => {
      console.error("[useFinalisePayslipsMutation] Mutation failed:", error);
    },
  });
}

/**
 * Hook to reopen selected payslips
 */
export function useReopenPayslipsMutation(periodId: string) {
  const ctx = useEmployerDBContext();
  const dbPath = ctx?.openEmployers.find(
    (e) => e.id === ctx.activeEmployerId,
  )?.dbPath;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (employeeIds: string[]) => {
      if (!dbPath) throw new Error("Database not available");
      if (!periodId) throw new Error("Period ID not available");
      if (!Array.isArray(employeeIds) || employeeIds.length === 0) {
        throw new Error("No employees selected");
      }

      try {
        return await reopenPayslips(dbPath, periodId, employeeIds);
      } catch (error) {
        console.error("[useReopenPayslipsMutation] Error:", error);
        throw error;
      }
    },
    onSuccess: () => {
      if (dbPath) {
        // Sequential cache invalidation to prevent race conditions
        setTimeout(() => {
          queryClient.invalidateQueries({
            queryKey: ["payslips", dbPath, periodId],
          });
        }, 0);

        setTimeout(() => {
          queryClient.invalidateQueries({ queryKey: ["payPeriods", dbPath] });
        }, 50);

        setTimeout(() => {
          queryClient.invalidateQueries({
            queryKey: ["payslipStatuses", dbPath, periodId],
          });
        }, 100);
      }
    },
    onError: (error) => {
      console.error("[useReopenPayslipsMutation] Mutation failed:", error);
    },
  });
}
