import { createClient } from "@libsql/client";
import * as schema from "../schema/employer";
import { drizzle } from "drizzle-orm/libsql";
import { eq } from "drizzle-orm";
import { employerSchema, type Employer } from "../../lib/schemas/employer";

/**
 * Returns a Drizzle adapter for an employer database.
 * @param filePath Path to the SQLite file.
 */
export function getEmployerDbAdapter(filePath: string) {
  try {
    if (!filePath || typeof filePath !== "string") {
      throw new Error("Invalid file path provided");
    }

    const client = createClient({
      url: `file:${filePath}`,
    });
    const db = drizzle(client, { schema });
    console.log("[EmployerDB] Database connection established:", filePath);
    return { db, client };
  } catch (error) {
    console.error("[EmployerDB] Failed to create database connection:", error);
    throw error;
  }
}

/**
 * Fetches the employer record from the employer DB and validates with <PERSON><PERSON>.
 * @param dbPath Path to the SQLite file.
 * @returns Employer object
 */
// Utility: convert all nulls in an object to undefined (shallow)
function nullsToUndefined<T>(obj: T): T {
  if (!obj || typeof obj !== "object") return obj;
  return Object.fromEntries(
    Object.entries(obj).map(([k, v]) => [k, v === null ? undefined : v]),
  ) as T;
}

export async function getEmployer(dbPath: string): Promise<Employer> {
  const { db, client } = getEmployerDbAdapter(dbPath);
  try {
    // There should only be one employer row per DB
    const result = await db.select().from(schema.employer).limit(1).all();
    if (!result || !result[0]) {
      throw new Error("No employer record found in DB");
    }
    // Convert nulls to undefined for Zod defaults
    const parsed = employerSchema.safeParse(nullsToUndefined(result[0]));
    if (!parsed.success) {
      throw new Error(
        "Employer validation failed: " + JSON.stringify(parsed.error.issues),
      );
    }
    return parsed.data;
  } finally {
    await client.close();
  }
}

/**
 * Updates the employer record in the employer DB, validates input/output with Zod.
 * @param dbPath Path to the SQLite file.
 * @param employer Employer object (validated)
 * @returns Updated Employer object
 */
export async function updateEmployer(
  dbPath: string,
  employer: Employer,
): Promise<Employer> {
  // Validate input
  const inputParsed = employerSchema.safeParse(employer);
  if (!inputParsed.success) {
    throw new Error(
      "Employer input validation failed: " +
        JSON.stringify(inputParsed.error.issues),
    );
  }
  const { db, client } = getEmployerDbAdapter(dbPath);
  try {
    // Update by id (should only be one row)
    const updated = await db
      .update(schema.employer)
      .set(employer)
      .where(eq(schema.employer.id, employer.id))
      .returning()
      .all();
    if (!updated || !updated[0]) {
      throw new Error("Failed to update employer record");
    }
    // Validate output (convert nulls to undefined for Zod)
    const outputParsed = employerSchema.safeParse(nullsToUndefined(updated[0]));
    if (!outputParsed.success) {
      throw new Error(
        "Employer output validation failed: " +
          JSON.stringify(outputParsed.error.issues),
      );
    }
    return outputParsed.data;
  } finally {
    await client.close();
  }
}
