import { ipcMain } from "electron";
import { getEmployerDb } from "../main/EmployerDbManager";
import * as schema from "../../drizzle/schema/employer/payslip";
import { eq } from "drizzle-orm";

// Rate limiting for database operations
const operationQueue = new Map<string, Promise<any>>();

async function queueOperation<T>(
  key: string,
  operation: () => Promise<T>,
): Promise<T> {
  // Wait for any existing operation with the same key
  if (operationQueue.has(key)) {
    try {
      await operationQueue.get(key);
    } catch {
      // Ignore errors from previous operations
    }
  }

  // Execute the new operation
  const promise = operation();
  operationQueue.set(key, promise);

  try {
    const result = await promise;
    operationQueue.delete(key);
    return result;
  } catch (error) {
    operationQueue.delete(key);
    throw error;
  }
}

// Fetch all additions item types
ipcMain.handle(
  "employerDb:getAdditionsItemTypes",
  async (_event, dbPath: string) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      let itemTypes = await db
        .select()
        .from(schema.payslipAdditionsItemTypes)
        .all();

      // Seed default types if none exist
      if (itemTypes.length === 0) {
        const now = Math.floor(Date.now() / 1000);
        const defaults = [
          {
            id: "bonus",
            code: "bonus",
            display_label: "Bonus",
            created_at: now,
            updated_at: now,
          },
          {
            id: "commission",
            code: "commission",
            display_label: "Commission",
            created_at: now,
            updated_at: now,
          },
          {
            id: "additional",
            code: "additional",
            display_label: "Additional Pay",
            created_at: now,
            updated_at: now,
          },
          {
            id: "holiday",
            code: "holiday",
            display_label: "Holiday Pay",
            created_at: now,
            updated_at: now,
          },
          {
            id: "expenses",
            code: "expenses",
            display_label: "Expenses",
            created_at: now,
            updated_at: now,
          },
        ];
        await db
          .insert(schema.payslipAdditionsItemTypes)
          .values(defaults)
          .run();
        itemTypes = await db
          .select()
          .from(schema.payslipAdditionsItemTypes)
          .all();
      }
      return { success: true, itemTypes };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Upsert an additions line item
ipcMain.handle(
  "employerDb:upsertPayslipAdditionsLineItem",
  async (_event, dbPath: string, item: any) => {
    const operationKey = `upsert-addition-${dbPath}-${item?.payslip_id}`;

    return queueOperation(operationKey, async () => {
      try {
        const entry = getEmployerDb(dbPath);
        if (!entry) return { success: false, error: "Employer DB is not open" };
        const db = entry.db;

        // Validate inputs
        if (!item || typeof item !== "object") {
          return { success: false, error: "Invalid item data" };
        }
        if (!item.payslip_id || typeof item.payslip_id !== "string") {
          return { success: false, error: "Invalid payslip ID" };
        }

        const now = Math.floor(Date.now() / 1000);

        if (!item.id) {
          const id = crypto.randomUUID();
          const [inserted] = await db
            .insert(schema.payslipAdditionsLineItems)
            .values({ ...item, id, created_at: now, updated_at: now })
            .returning();
          return { success: true, lineItem: inserted };
        } else {
          const [updated] = await db
            .update(schema.payslipAdditionsLineItems)
            .set({ ...item, updated_at: now })
            .where(eq(schema.payslipAdditionsLineItems.id, item.id))
            .returning();
          return { success: true, lineItem: updated };
        }
      } catch (err: any) {
        console.error("[IPC] Error upserting additions line item:", err);
        return {
          success: false,
          error: err.message || "Unknown error occurred",
        };
      }
    });
  },
);

// Delete an additions line item
ipcMain.handle(
  "employerDb:deletePayslipAdditionsLineItem",
  async (_event, dbPath: string, id: string) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const result = await db
        .delete(schema.payslipAdditionsLineItems)
        .where(eq(schema.payslipAdditionsLineItems.id, id));
      return { success: true, deletedCount: result.changes ?? 0 };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Clear all additions line items (set amounts to 0)
ipcMain.handle(
  "employerDb:clearAllPayslipAdditionsLineItems",
  async (_event, dbPath: string, payslipId: string) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const now = Math.floor(Date.now() / 1000);
      const result = await db
        .update(schema.payslipAdditionsLineItems)
        .set({ amount: 0, updated_at: now })
        .where(eq(schema.payslipAdditionsLineItems.payslip_id, payslipId));
      return { success: true, updatedCount: result.changes ?? 0 };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Delete all additions line items
ipcMain.handle(
  "employerDb:deleteAllPayslipAdditionsLineItems",
  async (_event, dbPath: string, payslipId: string) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const result = await db
        .delete(schema.payslipAdditionsLineItems)
        .where(eq(schema.payslipAdditionsLineItems.payslip_id, payslipId));
      return { success: true, deletedCount: result.changes ?? 0 };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);
